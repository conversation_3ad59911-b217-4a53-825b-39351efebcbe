{"cells": [{"cell_type": "code", "execution_count": null, "id": "ca9fb21a", "metadata": {}, "outputs": [], "source": ["# https://huggingface.co/datasets/cerebras/SlimPajama-627B\n", "from datasets import load_dataset\n", "ds = load_dataset(\"cerebras/SlimPajama-627B\", streaming=True)\n", "slimpajama_train, slimpajama_test = ds[\"train\"], ds[\"test\"]\n"]}, {"cell_type": "code", "execution_count": 26, "id": "8988b8c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'text': '<PERSON><PERSON><PERSON><PERSON> Returns To Write And Direct \\'Star Wars: Episode IX\\'\\n09/12/2017 11:11 am ET Updated Sep 12, 2017\\nThe return of the J.J.\\n<PERSON><PERSON> <PERSON>\\nUPDATE: 4:00 p.m. ET — In addition to the director news, \"Star Wars\" announced that the premiere date for \"Episode IX\" will be Dec. 20, 2019.\\nStar Wars: Episode IX is scheduled for release on December 20, 2019. pic.twitter.com/rDBqmuHX89\\n— Star Wars (@starwars) September 12, 2017\\nThe Force was with <PERSON><PERSON><PERSON><PERSON> when he launched the new set of \"Star Wars\" films with \"The Force Awakens,\" so now <PERSON> is bringing him back.\\nAs Deadline reported on Tuesday, and according to a press release on StarWars.com, <PERSON> will return to write and direct \"Star Wars: Episode IX.\" The statement reads:\\nA post shared by <PERSON> Wars (@starwars) on Sep 12, 2017 at 7:28am PDT\\nAfter Disney unexpectedly parted ways with former \"Episode IX\" director <PERSON> earlier this month, rumors that <PERSON><PERSON>, who is directing \"Star Wars: The Last Jedi,\" would take over surfaced. But Deadline reports that <PERSON> decided not to take the offer to direct.\\nOn <PERSON>\\' hiring, Lucasfilm President <PERSON> said, \"With \\'The Force Awakens,\\' <PERSON><PERSON><PERSON><PERSON> delivered everything we could have possibly hoped for, and I am so excited that he is coming back to close out this trilogy.\"\\nAfter what we saw in \"Force Awakens,\" we\\'re pretty excited about it, too. We just hope they call it \"The Return of the J.J.\"\\nThere Were 2 Royal Moments You Might Have Missed At Biden\\'s Inauguration Joe Biden Removed Trump\\'s Diet Coke Button, Twitter Bubbled Up With Jokes Princess Charlene Defends New Buzzcut Hairstyle: \\'It Was My Decision\\' Katy Perry Closes Out Biden\\'s Inauguration Celebration With A Literal Bang\\n\\'Star Wars\\' Postage Stamps\\nEntertainment Editor, HuffPost\\nMovies Star Wars J.J. Abrams', 'meta': {'redpajama_set_name': 'RedPajamaCommonCrawl'}}\n", "{'text': 'Hire event staffing in Barrie, Ontario and discover how Push Agency can help you create the right brand perception and increase exposure.\\nBarrie, ON – Looking for a superior staff that will increase sales? PUSH has your solution. Our revolutionary live marketing tactics provide our clients with the very best the industry can offer. Clients are given access to an array of amenities enhancing their experience with us. PUSH works around the clock to ensure that your event goes off without a hitch. Each client gets access to set up our database to tailor their needs. They are able to set up their very own user console to review events, select models, set up trainings, and so much more. Give us a call when planning your next Barrie area event to learn how a little PUSH can make a big impact!', 'meta': {'redpajama_set_name': 'RedPajamaC4'}}\n"]}], "source": ["for idx, item in enumerate(slimpajama_train):\n", "    print(item)\n", "    if idx > 0:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "id": "8a133a02", "metadata": {}, "outputs": [], "source": ["\n", "# tiktoken\n", "import tiktoken\n", "# tokenizer = tiktoken.get_encoding(\"gpt2\")\n", "# tiktoken.encoding_for_model(\"gpt-4o\")\n", "\n", "# cl100k_base\n", "tokenizer = tiktoken.get_encoding(\"cl100k_base\")  # used for gpt-3.5 ad gpt-4\n"]}, {"cell_type": "code", "execution_count": null, "id": "0dd71c48", "metadata": {}, "outputs": [{"ename": "DatasetNotFoundError", "evalue": "Dataset 'stackv2' doesn't exist on the Hub or cannot be accessed.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mDatasetNotFoundError\u001b[39m                      <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[17]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# load the stackv2\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m ds = \u001b[43mload_dataset\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstackv2\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstreaming\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/datasets/load.py:1392\u001b[39m, in \u001b[36mload_dataset\u001b[39m\u001b[34m(path, name, data_dir, data_files, split, cache_dir, features, download_config, download_mode, verification_mode, keep_in_memory, save_infos, revision, token, streaming, num_proc, storage_options, **config_kwargs)\u001b[39m\n\u001b[32m   1387\u001b[39m verification_mode = VerificationMode(\n\u001b[32m   1388\u001b[39m     (verification_mode \u001b[38;5;129;01mor\u001b[39;00m VerificationMode.BASIC_CHECKS) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m save_infos \u001b[38;5;28;01melse\u001b[39;00m VerificationMode.ALL_CHECKS\n\u001b[32m   1389\u001b[39m )\n\u001b[32m   1391\u001b[39m \u001b[38;5;66;03m# Create a dataset builder\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1392\u001b[39m builder_instance = \u001b[43mload_dataset_builder\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1393\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1394\u001b[39m \u001b[43m    \u001b[49m\u001b[43mname\u001b[49m\u001b[43m=\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1395\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1396\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1397\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1398\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfeatures\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfeatures\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1399\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1400\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1401\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1402\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1403\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1404\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mconfig_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1405\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1407\u001b[39m \u001b[38;5;66;03m# Return iterable dataset in case of streaming\u001b[39;00m\n\u001b[32m   1408\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m streaming:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/datasets/load.py:1132\u001b[39m, in \u001b[36mload_dataset_builder\u001b[39m\u001b[34m(path, name, data_dir, data_files, cache_dir, features, download_config, download_mode, revision, token, storage_options, **config_kwargs)\u001b[39m\n\u001b[32m   1130\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m features \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   1131\u001b[39m     features = _fix_for_backward_compatible_features(features)\n\u001b[32m-> \u001b[39m\u001b[32m1132\u001b[39m dataset_module = \u001b[43mdataset_module_factory\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1133\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1134\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1135\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1136\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdownload_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1137\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1138\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_files\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1139\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1140\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1141\u001b[39m \u001b[38;5;66;03m# Get dataset builder class\u001b[39;00m\n\u001b[32m   1142\u001b[39m builder_kwargs = dataset_module.builder_kwargs\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/datasets/load.py:1025\u001b[39m, in \u001b[36mdataset_module_factory\u001b[39m\u001b[34m(path, revision, download_config, download_mode, data_dir, data_files, cache_dir, **download_kwargs)\u001b[39m\n\u001b[32m   1023\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCouldn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt reach the Hugging Face Hub for dataset \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me1\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[32m   1024\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e1, (DataFilesNotFoundError, DatasetNotFoundError, EmptyDatasetError)):\n\u001b[32m-> \u001b[39m\u001b[32m1025\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e1 \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1026\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e1, \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m):\n\u001b[32m   1027\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m(\n\u001b[32m   1028\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCouldn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt find any data file at \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrelative_to_absolute_path(path)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1029\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCouldn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt find \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m on the Hugging Face Hub either: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(e1).\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me1\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m   1030\u001b[39m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/datasets/load.py:980\u001b[39m, in \u001b[36mdataset_module_factory\u001b[39m\u001b[34m(path, revision, download_config, download_mode, data_dir, data_files, cache_dir, **download_kwargs)\u001b[39m\n\u001b[32m    976\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m DatasetNotFoundError(\n\u001b[32m    977\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mRevision \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrevision\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m doesn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt exist for dataset \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m on the Hub.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    978\u001b[39m     ) \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m    979\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m RepositoryNotFoundError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m980\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m DatasetNotFoundError(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mDataset \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m doesn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt exist on the Hub or cannot be accessed.\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m    981\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    982\u001b[39m     api.hf_hub_download(\n\u001b[32m    983\u001b[39m         repo_id=path,\n\u001b[32m    984\u001b[39m         filename=filename,\n\u001b[32m   (...)\u001b[39m\u001b[32m    987\u001b[39m         proxies=download_config.proxies,\n\u001b[32m    988\u001b[39m     )\n", "\u001b[31mDatasetNotFoundError\u001b[39m: Dataset 'stackv2' doesn't exist on the Hub or cannot be accessed."]}], "source": ["# load the stackv2\n", "\n", "ds = load_dataset(\"stackv2\", streaming=True)"]}, {"cell_type": "code", "execution_count": 15, "id": "e72dbc38", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1811\n", "tokenized length: 457\n", "797\n", "tokenized length: 160\n", "3173\n", "tokenized length: 726\n", "496\n", "tokenized length: 115\n", "5154\n", "tokenized length: 1070\n", "1888\n", "tokenized length: 367\n", "1823\n", "tokenized length: 404\n", "2218\n", "tokenized length: 434\n", "6098\n", "tokenized length: 1256\n", "5806\n", "tokenized length: 1710\n", "1666\n", "tokenized length: 365\n", "944\n", "tokenized length: 191\n"]}], "source": ["for idx, item in enumerate(ds[\"train\"]):\n", "    item[\"length\"] = len(item[\"text\"])\n", "    print(item[\"length\"])\n", "    print(f\"tokenized length: {len(tokenizer.encode(item['text']))}\")\n", "    if idx > 10:\n", "        break\n"]}, {"cell_type": "code", "execution_count": 18, "id": "4576ecd6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["list(file.keys())=['blob_id', 'path', 'content_id', 'language', 'length_bytes', 'detected_licenses', 'license_type', 'src_encoding', 'is_vendor', 'is_generated', 'alphanum_fraction', 'alpha_fraction', 'num_lines', 'avg_line_length', 'max_line_length']\n", "list(file.keys())=['blob_id', 'path', 'content_id', 'language', 'length_bytes', 'detected_licenses', 'license_type', 'src_encoding', 'is_vendor', 'is_generated', 'alphanum_fraction', 'alpha_fraction', 'num_lines', 'avg_line_length', 'max_line_length']\n", "list(file.keys())=['blob_id', 'path', 'content_id', 'language', 'length_bytes', 'detected_licenses', 'license_type', 'src_encoding', 'is_vendor', 'is_generated', 'alphanum_fraction', 'alpha_fraction', 'num_lines', 'avg_line_length', 'max_line_length']\n", "list(file.keys())=['blob_id', 'path', 'content_id', 'language', 'length_bytes', 'detected_licenses', 'license_type', 'src_encoding', 'is_vendor', 'is_generated', 'alphanum_fraction', 'alpha_fraction', 'num_lines', 'avg_line_length', 'max_line_length']\n", "================================================================================\n", "/README.md\n", "file[\"is_generated\"]=False\n", "file[\"language\"]='Markdown'\n", "file[\"num_lines\"]=11\n", "file[\"length_bytes\"]=262\n", "--------------------------------------------------------------------------------\n", "# To reproduce:\n", "\n", "\tnvm install iojs-3.2.0\n", "\tnvm use iojs-3.2.0\n", "\n", "\twhich npm # verify that this is from the iojs-3.2.0 directory\n", "\tnpm install -g pangyp\n", "\tpangyp configure build\n", "\n", "\twhich node # verify that this is from the iojs-3.2.0 directory\n", "\tnode --trace-gc test.js\n", "\n", "================================================================================\n", "/src/array-buffer-recycler.cc\n", "file[\"is_generated\"]=False\n", "file[\"language\"]='C++'\n", "file[\"num_lines\"]=38\n", "file[\"length_bytes\"]=1028\n", "--------------------------------------------------------------------------------\n", "\n", "#include <node.h>\n", "#include <v8.h>\n", "\n", "namespace module {\n", "\n", "using v8::FunctionCallbackInfo;\n", "using v8::Isolate;\n", "using v8::Local;\n", "using v8::Object;\n", "using v8::Value;\n", "using v8::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;\n", "\n", "void Recycle(const FunctionCallbackInfo<Value>& args) {\n", "  Isolate* isolate = args.GetIsolate();\n", "  if (not args[0]->IsArrayBuffer()) { return; }\n", "  auto buffer = Local<ArrayBuffer>::Cast(args[0]);\n", "  auto contents = buffer->Externalize();\n", "  buffer->Neuter();\n", "\n", "  auto newBuffer = ArrayBuffer::New(isolate, contents.Data(), contents.ByteLength(),\n", "                                    v8::ArrayBufferCreationMode::kInternalized);\n", "  args.GetReturnValue().Set(newBuffer);\n", "}\n", "\n", "void Create(const FunctionCallbackInfo<Value>& args) {\n", "  Isolate* isolate = args.GetIsolate();\n", "  auto newBuffer = ArrayBuffer::New(isolate, 1024 * 1024);\n", "  args.GetReturnValue().Set(newBuffer);\n", "}\n", "\n", "void init(Local<Object> exports) {\n", "  NODE_SET_METHOD(exports, \"recycle\", Recycle);\n", "  NODE_SET_METHOD(exports, \"create\", Create);\n", "}\n", "\n", "}\n", "\n", "NODE_MODULE(arraybufferrecycler, module::init)\n", "\n", "================================================================================\n", "/test.js\n", "file[\"is_generated\"]=False\n", "file[\"language\"]='JavaScript'\n", "file[\"num_lines\"]=12\n", "file[\"length_bytes\"]=408\n", "--------------------------------------------------------------------------------\n", "\n", "var recycle = require('./build/Release/array-buffer-recycler').recycle;\n", "var create = require('./build/Release/array-buffer-recycler').create;\n", "var buffer = new ArrayBuffer(1024 * 1024);\n", "\n", "while (true) {\n", "  // not expected to trigger gc because no new memory is being allocated.\n", "  // Heap is not growing.\n", "  buffer = recycle(buffer);\n", "\n", "  // expected to trigger GCs because of allocation.\n", "  //buffer = create();\n", "}\n", "\n", "================================================================================\n", "/binding.gyp\n", "file[\"is_generated\"]=False\n", "file[\"language\"]='Python'\n", "file[\"num_lines\"]=20\n", "file[\"length_bytes\"]=416\n", "--------------------------------------------------------------------------------\n", "{\n", "  \"targets\": [\n", "    {\n", "      \"target_name\": \"array-buffer-recycler\",\n", "      \"sources\": [\n", "        \"src/array-buffer-recycler.cc\",\n", "      ],\n", "      \"cflags_cc\": [ \"-std=c++11\" ],\n", "      'conditions': [\n", "        [ 'OS==\"mac\"', {\n", "\n", "          'xcode_settings': {\n", "            'OTHER_CPLUSPLUSFLAGS' : ['-std=c++11', '-stdlib=libc++'],\n", "            'MACOSX_DEPLOYMENT_TARGET': '10.7'\n", "          },\n", "        }],\n", "      ],\n", "    }\n", "  ]\n", "}\n", "\n", "list(file.keys())=['blob_id', 'path', 'content_id', 'language', 'length_bytes', 'detected_licenses', 'license_type', 'src_encoding', 'is_vendor', 'is_generated', 'alphanum_fraction', 'alpha_fraction', 'num_lines', 'avg_line_length', 'max_line_length']\n"]}], "source": ["import os\n", "import boto3\n", "from smart_open import open\n", "from datasets import load_dataset\n", "from dotenv import load_dotenv\n", "\n", "# Load from a specific file\n", "load_dotenv(dotenv_path=os.path.expanduser(\"~/.aws/.env\"))\n", "\n", "session = boto3.Session(\n", "    aws_access_key_id=os.environ[\"AWS_ACCESS_KEY_ID\"],\n", "    aws_secret_access_key=os.environ[\"AWS_SECRET_ACCESS_KEY\"])\n", "s3 = session.client(\"s3\")\n", "\n", "def download_contents(files):\n", "    for file in files:\n", "        s3_url = f\"s3://softwareheritage/content/{file['blob_id']}\"\n", "        print(f\"{list(file.keys())=}\")\n", "        with open(s3_url, \"rb\", compression=\".gz\", transport_params={\"client\": s3}) as fin:\n", "            file[\"content\"] = fin.read().decode(file[\"src_encoding\"])\n", "    \n", "    return {\"files\": files}\n", "\n", "ds = load_dataset(\"bigcode/the-stack-v2-train-full-ids\", split=\"train\", streaming=True)\n", "ds = ds.map(lambda row: download_contents(row[\"files\"]))\n", "for idx, row in enumerate(ds):\n", "    if idx == 0:\n", "        for file in row[\"files\"]:\n", "            print(\"=\" * 80)\n", "            print(file[\"path\"])\n", "            print(f'{file[\"is_generated\"]=}')\n", "            print(f'{file[\"language\"]=}')\n", "            print(f'{file[\"num_lines\"]=}')\n", "            print(f'{file[\"length_bytes\"]=}')\n", "            print(\"-\" * 80)\n", "            print(file[\"content\"])\n", "    if idx > 0:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "id": "5d7f513a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "2018f166", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/PILA/main.cpp\n", "#include \"pila.hpp\"\n", "\n", "int main(void) {\n", " \n", "  pila A;\n", "  \n", "  \n", "    \n", "  <PERSON><PERSON>push(5);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>push(4);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>push(3);\n", "  <PERSON><PERSON>();\n", "  \n", "  \n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  \n", "  return 0;\n", "}\n", "/PILA/nodo.hpp\n", "#pragma once\n", "#include \"common.hpp\"\n", "#include <iostream>\n", "\n", "class mynodo {\n", "  \n", "    TDATO dato; //elemento que tiene el nodo\n", "    mynodo* next; //puntero al siguiente nodo\n", "  \n", "  public:\n", "    mynodo(TDATO ele); //constructor que añade un elemento\n", "    mynodo(void); //constructor por defecto\n", "    ~mynodo(void); //destructor por defecto\n", "   \n", "    void set_dato(TDATO ele); //metodo para cambiar el elemento\n", "    TDATO get_dato(void); //método para saber el valor del elemento del nodo\n", "    mynodo* get_next(void); // método para obtener el siguiente nodo\n", "    void set_next(mynodo* inx); //método para determinar el siguiente nodo\n", "};\n", "\n", "/VECTOR/main.cpp\n", "#include \"vector.hpp\"\n", "\n", "myvector operator+(myvector& A,myvector& B) {\t//operador de suma sorecargado que suma los elementos de dos vectores\n", "\n", "  \n", "\n", "  if(A.tamanio() <= B.tamanio()) {\n", "    myvector C(B);\n", "    unsigned int i;\n", "    for(i =0;i<A.tamanio();i++) {\n", "    \n", "      <PERSON>.dejar_elemento(i, A[i] + C[i]);\n", "      \n", "    }\n", "    \n", "    return C;\n", "      \n", "   } else {\n", "    \n", "      myvector C(A);\n", "      unsigned int i;\n", "      for(i =0;i<B.tamanio();i++) {\n", "    \n", "      <PERSON><PERSON>dejar_elemento(i, B[i] + C[i]);\n", "      \n", "      }\n", "      \n", "      return C;\n", "      \n", "    }\n", "    \n", "    \n", "}\n", "\n", "std::ostream& operator<<(std::ostream& os, const myvector& v) {\n", "\n", "\tfor(int i = 0; i<v.tam;i++) {\n", "\t\t\n", "\t\tos << v[i] << \" \";\n", "\t\t\n", "\t}\n", "\n", "}\n", "\n", "int main(void) {\n", "\t\n", "\t\n", "\tmyvector A(5);\n", "\tmyvector B(6);\n", "\tmyvector C;\n", "\tstd::cout << \"Introduzca los elementos:\" << std::endl;\n", "\t\n", "\tint i;\n", "\tTDATO dato;\n", "\tfor(i=0; i<A.tamanio(); i++) {\n", "\t\t\n", "\t\tstd::cout << i+1 << \"º:\";\n", "\t\tstd::cin >> dato;\n", "\t\tstd::cout << std::endl;\n", "\t\t<PERSON><PERSON>dejar_elemento(i,dato);\n", "\t}\n", "\t\n", "\tint j;\n", "\tfor(j=0; j<<PERSON>.tamanio(); j++) {\n", "\t\t\n", "\t\tstd::cout << j+1 << \"º:\";\n", "\t\tstd::cin >> dato;\n", "\t\tstd::cout << std::endl;\n", "\t\t<PERSON><PERSON>dejar_elemento(j,dato);\n", "\t}\n", "\t\n", "\t\n", "\tstd::cout << B[5] << std::endl;\n", "\t\n", "\tstd::cout << A << std::endl;\n", "\t<PERSON><PERSON>();\n", "\tstd::cout << A[2] << std::endl;\n", "\t\n", "\tC = A + B; //suma los elementos de los vectores y los guarda en C\n", "\t\n", "\tstd::cout << \"Vector sumado: \" << C << std::endl;\n", "\t\n", "\t\n", "}\n", "\n", "\n", "/LISTA/lista.cpp\n", "#include \"lista.hpp\"\n", "\n", "listilla::listilla(void):\n", "front(NULL),\n", "rear(NULL),\n", "tam(0)\n", "{}\n", "\n", "listilla::~listilla(void) {\n", "\n", "  mynodo* aux = front;\n", "  while(front!=NULL) {\n", "  \n", "    front = front->get_next();\n", "    delete aux;\n", "    aux=front;\n", "    \n", "  }\n", "  \n", "}  \n", "\n", "bool listilla::empty(void) {\n", "\n", "  return(front == NULL ? true : false);\n", "  \n", "}\n", "\n", "void listilla::insertar_vacio(TDATO elemento) {\n", "  \n", "  mynodo* dummy = new mynodo(elemento);\n", "  front = dummy;\n", "  rear = dummy;\n", "  tam++;\n", "}\n", "\n", "void listilla::insertar_inicio(TDATO elemento) {\n", "\n", "  if(empty()) {\n", "    \n", "    insertar_vacio(elemento);\n", "    \n", "  } else {\n", "    \n", "      mynodo* dummy = new mynodo(elemento);\n", "    \n", "    if(front == rear) {\n", "      \n", "      dummy->set_next(rear);\n", "      front = dummy;\n", "    \n", "    } else {\n", "    \n", "      dummy->set_next(front);\n", "      front = dummy;\n", "    }\n", "   tam++;\n", "  }\n", "\n", "}\n", "\n", "TDATO listilla::extraer_inicio(void) {\n", "\n", "  if(empty()) {\n", "    \n", "    std::cout << \"La lista ya está completamente vacia\" << std::endl;\n", "    \n", "  } else  {\n", "    \n", "\tif(front==rear) {\n", "\t  mynodo* aux = front;\n", "\t  front = NULL;\n", "\t  rear = NULL;\n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t} else {\n", "  \n", "\t  mynodo* aux=front;\n", "\t  front = front->get_next();\n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t}\n", "  }\n", "  \n", "}\n", "\n", "void listilla::insertar_final(TDATO elemento) {\n", "  \n", "   mynodo* dummy = new mynodo(elemento);\n", "\n", "  if(empty()) {\n", "    \n", "    insertar_vacio(elemento);\n", "  \n", "  } else {\n", "    \n", "    rear->set_next(dummy);\n", "    rear=dummy;\n", "    \n", "  }\n", "  tam++;\n", "}\n", "\n", "TDATO listilla::extraer_final(void) {\n", "  \n", "  if(empty()) {\n", "    \n", "    std::cout << \"La lista ya está completamente vacia\" << std::endl;\n", "    \n", "  } else {\n", "  \n", "\tif(front==rear) {\n", "    \n", "\t  mynodo* aux = front;\n", "\t  front = NULL;\n", "\t  rear = NULL;\n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t} else {\n", "  \n", "\t  mynodo* aux=front;\n", "    \n", "\t  while(aux->get_next() != rear) {\n", "    \n", "\t    aux=aux->get_next();\n", "      \n", "\t  }\n", "    \n", "\t  rear = aux;\n", "\t  aux = aux->get_next();\n", "\t  rear->set_next(NULL);\n", "    \n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t }\n", "  }\n", "  \n", "}\n", "\n", "void listilla::escri<PERSON>(void) {\n", "  \n", "  \n", "  mynodo* aux = front;\n", "  \n", "  while(aux != NULL) {\n", "  \n", "    std::cout << aux->get_dato() << \" \";\n", "    aux= aux->get_next();\n", "  }\n", "  \n", "  std::cout << std::endl;\n", "  \n", "  //std::cout << tam << std::endl;\n", "}\n", "\n", "\n", "\n", "void listilla::insertar_despues(TDATO elemento, unsigned int pos) {\n", " \n", "  if(pos == 0){\n", "  \n", "    insertar_inicio(elemento);\n", "    \n", "  } else { if(pos==tam-1) {\n", "  \n", "    insertar_final(elemento);\n", "    \n", "    } else { if(pos>tam-1) {\n", "      \n", "      std::cout << \"ERROR, la posición solicitada es mayor que el tamaño de la lista\" << std::endl;\n", "      std::cout << \"Si quiere expandirla por los extremos insete al final o al principio\" << std::endl;\n", "      \n", "      } else {\n", "      \n", "\tmynodo* dummy = new mynodo(elemento);  \n", "\t\n", "\tmynodo* aux = front;\n", "\tunsigned int i;\n", "\tfor(i=1;i<pos;i++){\n", "\t\n", "\t  aux = aux->get_next();\n", "\t  \n", "\t}\n", "\t\n", "\tdummy->set_next(aux->get_next());\n", "\taux->set_next(dummy);\n", "\ttam++;\n", "\t\n", "      }\n", "  \n", "    }\n", "  }\n", "  \n", "}\n", "\n", "\n", "TDATO listilla::extraer(unsigned int pos) {\n", "\n", "  if(empty()) {\n", "    \n", "    std::cout << \"La lista ya está completamente vacia\" << std::endl;\n", "    \n", "  } else {\n", "  \n", "\tif(pos == 0){\n", "  \n", "\t  return(extraer_inicio());\n", "    \n", "\t} else { if(pos==tam-1) {\n", "  \n", "\t  return(extraer_final());\n", "    \n", "\t  } else { if(pos>tam-1) {\n", "      \n", "\t    std::cout << \"ERROR, la posición solicitada es mayor que el tamaño de la lista\" << std::endl;\n", "      \n", "\t    } else {\n", "      \n", "\t      mynodo* aux = front;mynodo* aux2=front;\n", "\t      unsigned int i,j;\n", "\t      for(i=1;i<pos;i++){\n", "\t\n", "\t\taux = aux->get_next();\n", "\t  \n", "\t      } for(j=1;j<pos-1;j++) {\n", "\t\n", "\t\taux2 = aux2->get_next();\n", "\t  \n", "\t      }\n", "\t\n", "\t      aux2->set_next(aux->get_next());\n", "\t      tam--;\n", "\t      return (aux->get_dato());\n", "\t\n", "\t    }\n", "  \n", "\t  }\n", "\t}\n", "\t\n", "   }\n", "  \n", "}\n", "\n", "\n", "void listilla::edicion(TDATO dato, unsigned int pos) {\n", "\n", "  if((pos<tam-1) || (pos<0)) {\n", "      \n", "      std::cout << \"ERROR, la posición solicitada es mayor que el tamaño de la lista\" << std::endl;\n", "      \n", "  } else {\n", "      \n", "\tmynodo* aux = front;\n", "\tunsigned int i;\n", "\tfor(i=1;i<pos;i++){\n", "\t\n", "\t  aux = aux->get_next();\n", "\t  \n", "\t}\n", "\t\n", "\taux->set_dato(dato);\n", "\t\n", "      }\n", "  \n", "}\n", "\n", "\n", "\n", "/VECTOR/vector.cpp\n", "#include \"vector.hpp\"\n", "\n", "\n", "myvector::myvector(void):\t//constructor por defecto\n", "tam(0),\n", "elemento(NULL)\n", "{}\n", "\n", "myvector::myvector(unsigned int size):\n", "tam(size),\n", "elemento(NULL)\n", "{\n", "\telemento = new TDATO [tam];\n", "\t}\n", "\t\n", "\n", "myvector::myvector(myvector& A): \t//constructor de copia\n", "tam(0),\n", "elemento(NULL)\n", "{\n", "   /* tam = <PERSON>.tamanio();\n", "    elemento = new TDATO [tam];\n", "    \n", "    unsigned int i;\n", "    for(i=0;i<tam;i++) {\n", "     \n", "      elemento[i] = A[i];\n", "      \n", "    }*/\n", "  *this = A;\n", "  \n", "}\n", "\t\n", "myvector::~myvector(void) {\n", "\t\n", "\tif (elemento != NULL){\n", "\t\t\tdelete [] elemento;\n", "\t\t\telemento = NULL;\n", "\t\t}\n", "\t}\n", "\n", "void myvector::mostrarVector(void) { //funcio para mostrar el vector entero\n", "\t\n", "\tunsigned int i;\n", "\tfor(i=0;i<tam;i++) {\n", "\t\t\n", "\t\tstd::cout << elemento[i] << \" \";\n", "\t}\n", "\t\n", "\tstd::cout << std::endl;\n", "}\n", "\n", "TDATO myvector::cojer_elemento(unsigned int i) {\n", "\t\n", "\tif( i < tam) {\n", "\t\treturn (elemento[i]);\n", "\t}\n", "\t\n", "}\n", "\n", "void myvector::dejar_elemento(unsigned int i, TDATO elem) {\n", "\t\n", "\tif(i < tam) {\n", "\t\telemento[i] = elem;\n", "\t} else {\n", "\t\tstd::cout << \"Error al depositar el elemento\" << std::endl;\n", "\t}\n", "}\n", "\t\n", "unsigned int myvector::tamanio(void) const {\n", "\t\n", "\treturn (tam);\n", "}\n", "\n", "\n", "\n", "TDATO myvector::operator[](unsigned int i) const {\t//operador que sobrecarga para acceder a un elemento del vector\n", "\n", "  return(elemento[i]);\n", "  \n", "}\n", "\n", "myvector& myvector::operator=(const myvector& x) {\t//operador de asignación\n", "\n", "  if (tam != x.tamanio()) {\n", "    tam = x.tamanio();\n", "    delete[] elemento;\n", "    elemento = new int[x.tamanio()];\n", "  }\n", "  // pasamos todos los elementos desde 'x'\n", "  for (unsigned int i = 0; i < tam; i++)\n", "    elemento[i] = x[i];\n", "\n", "  return *this;\n", "\n", "  \n", "}\n", "\n", "\n", "\n", "\n", "/PILA/pila.cpp\n", "#include \"pila.hpp\"\n", "\n", "pila::pila(void):\n", "front(NULL),\n", "rear(NULL),\n", "tam(0)\n", "{}\n", "\n", "pila::~pila(void) {\n", "\n", "  mynodo* aux = front;\n", "  while(front!=NULL) {\n", "  \n", "    front = front->get_next();\n", "    delete aux;\n", "    aux=front;\n", "    \n", "  }\n", "  \n", "}  \n", "\n", "bool pila::empty(void) {\n", "\n", "  return(front == NULL ? true : false);\n", "  \n", "}\n", "\n", "void pila::insertar_vacio(TDATO elemento) {\n", "  \n", "  mynodo* dummy = new mynodo(elemento);\n", "  front = dummy;\n", "  rear = dummy;\n", "  tam++;\n", "}\n", "\n", "void pila::push(TDATO elemento) {\n", "\n", "  if(empty()) {\n", "    \n", "    insertar_vacio(elemento);\n", "    \n", "  } else {\n", "    \n", "      mynodo* dummy = new mynodo(elemento);\n", "    \n", "    if(front == rear) {\n", "      \n", "      dummy->set_next(rear);\n", "      front = dummy;\n", "    \n", "    } else {\n", "    \n", "      dummy->set_next(front);\n", "      front = dummy;\n", "    }\n", "   tam++;\n", "  }\n", "\n", "}\n", "\n", "TDATO pila::pop(void) {\n", "\n", "  if(empty()) {\n", "    \n", "    std::cout << \"La pila ya está completamente vacia\" << std::endl;\n", "    \n", "  } else  {\n", "    \n", "\tif(front==rear) {\n", "\t  mynodo* aux = front;\n", "\t  front = NULL;\n", "\t  rear = NULL;\n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t} else {\n", "  \n", "\t  mynodo* aux=front;\n", "\t  front = front->get_next();\n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t}\n", "  }\n", "  \n", "}\n", "\n", "void pila::escri<PERSON>(void) {\n", "  \n", "  \n", "  mynodo* aux = front;\n", "  \n", "  while(aux != NULL) {\n", "  \n", "    std::cout << aux->get_dato() << \" \";\n", "    aux= aux->get_next();\n", "  }\n", "  \n", "  std::cout << std::endl;\n", "  \n", "  //std::cout << tam << std::endl;\n", "}\n", "\n", "\n", "\n", "/LISTA/main.cpp\n", "#include \"lista.hpp\"\n", "\n", "\n", "int main(void) {\n", " \n", "  listilla A;\n", "  \n", "  \n", "    \n", "  <PERSON><PERSON>insertar_inicio(5);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>insertar_inicio(4);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>insertar_inicio(3);\n", "  <PERSON><PERSON>();\n", "  \n", "  <PERSON><PERSON>ar_final(6);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>insertar_final(7);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>insertar_final(8);\n", "  <PERSON><PERSON>();\n", "  \n", "  <PERSON>.extraer_inicio();\n", "  <PERSON><PERSON>();\n", "  \n", "  A.extraer_final();\n", "  <PERSON><PERSON>();\n", "  \n", "  <PERSON><PERSON><PERSON>_despues(9, 2);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>(2);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON><PERSON><PERSON>_despues(20,3);\n", "  <PERSON><PERSON>();\n", "  \n", "  A.extraer_final();\n", "  <PERSON><PERSON>();\n", "  A.extraer_final();\n", "  <PERSON><PERSON>();\n", "  A.extraer_final();\n", "  <PERSON><PERSON>();\n", "  A.extraer_final();\n", "  <PERSON><PERSON>();\n", "  A.extraer_final();\n", "  <PERSON><PERSON>();\n", "  A.extraer_final();\n", "  <PERSON><PERSON>();\n", "  \n", "  <PERSON><PERSON>ar_final(6);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>insertar_final(7);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>insertar_final(8);\n", "  <PERSON><PERSON>();\n", "}\n", "/VECTOR/vector.hpp\n", "#pragma once\n", "#include \"common.hpp\"\n", "#include <iostream>\n", "\n", "//typedef int TDATO;\n", "\n", "class myvector {\n", "\t\n", "\tprivate:\n", "\t\tTDATO* elemento; //puntero indicador de donde empieza el vector\n", "\t\tunsigned int tam; //tama<PERSON> del vector\n", "\t\n", "\tpublic:\n", "\t\tmyvector (void);\n", "\t\tmyvector (unsigned int size);\n", "\t\tmyvector (myvector& A);\n", "\t\t~myvector (void); //destructor por defecto\n", "\t\t\n", "\t\tfriend myvector operator+(const myvector&, const myvector&);\n", "\t\t\n", "\t\tTDATO operator[](unsigned int i) const;\n", "\t\tvoid mostrarVector(void); //método para ver el vector por pantalla\n", "\t\tTDATO cojer_elemento(unsigned int i); //método para seleccionar un elemento del vector\n", "\t\tvoid dejar_elemento(unsigned int i, TDATO elem); //método para introducir un elemento en el vector\n", "\t\t\n", "\t\tunsigned int tamanio(void) const; //método para que el usuario sepa el tamaño del vector\n", "\t\t\n", "\t\tmyvector& operator=(const myvector&);\n", "\t\tfriend std::ostream& operator<<(std::ostream&, const myvector&);\n", "\t\t\n", "\t};\n", "\n", "/PILA/pila.hpp\n", "#include \"nodo.hpp\"\n", "\n", "class pila {\n", "\n", "    mynodo* front;\n", "    mynodo* rear;\n", "    unsigned int tam;\n", "  \n", "  public:\n", "    pila(void);\n", "    ~pila(void);\n", "    \n", "    void push(TDATO elemento);\n", "    TDATO pop(void);\n", "    void insertar_vacio(TDATO elemento);\n", "   \n", "    bool empty(void);\n", "    void escribir(void);\n", "    \n", "};\n", "\n", "/COLA/common.hpp\n", "typedef int TDATO;\n", "\n", "/PILA/nodo.cpp\n", "#include \"nodo.hpp\"\n", "\n", "mynodo::mynodo(TDATO ele):\n", "dato(ele),\n", "next(NULL)\n", "{}\n", "\n", "mynodo::~mynodo(void) {}\n", "\n", "void mynodo::set_dato(TDATO ele) {\n", "  dato=ele;\n", "}\n", "\n", "TDATO mynodo::get_dato(void) { \n", "  return(dato);\n", "}\n", "\n", "mynodo* mynodo::get_next(void) {\n", "  return(next);\n", "}\n", "\n", "void mynodo::set_next(mynodo* A) {\n", "  next = A;\n", "}\n", "/LISTA/lista.hpp\n", "#include \"nodo.hpp\"\n", "\n", "class listilla {\n", "\n", "    mynodo* front;\n", "    mynodo* rear;\n", "    unsigned int tam;\n", "  \n", "  public:\n", "    listilla(void);\n", "    ~listilla(void);\n", "    \n", "    void insertar_inicio(TDATO elemento);\n", "    TDATO extraer_inicio(void);\n", "    void insertar_final(TDATO elemento);\n", "    TDATO extraer_final(void);\n", "    void insertar_vacio(TDATO elemento);\n", "   \n", "    bool empty(void);\n", "    void escribir(void);\n", "    void insertar_despues(TDATO elemento, unsigned int pos);\n", "    TDATO extraer(unsigned int pos);\n", "    void edicion(TDATO dato, unsigned int pos);\n", "};\n", "\n", "/COLA/cola.cpp\n", "#include \"cola.hpp\"\n", "\n", "cola::cola(void):\n", "front(NULL),\n", "rear(NULL),\n", "tam(0)\n", "{}\n", "\n", "cola::~cola(void) {\n", "\n", "  mynodo* aux = front;\n", "  while(front!=NULL) {\n", "  \n", "    front = front->get_next();\n", "    delete aux;\n", "    aux=front;\n", "    \n", "  }\n", "  \n", "}  \n", "\n", "bool cola::empty(void) {\n", "\n", "  return(front == NULL ? true : false);\n", "  \n", "}\n", "\n", "void cola::insertar_vacio(TDATO elemento) {\n", "  \n", "  mynodo* dummy = new mynodo(elemento);\n", "  front = dummy;\n", "  rear = dummy;\n", "  tam++;\n", "}\n", "\n", "\n", "\n", "TDATO cola::pop(void) {\n", "\n", "  if(empty()) {\n", "    \n", "    std::cout << \"La cola ya está completamente vacia\" << std::endl;\n", "    \n", "  } else  {\n", "    \n", "\tif(front==rear) {\n", "\t  mynodo* aux = front;\n", "\t  front = NULL;\n", "\t  rear = NULL;\n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t} else {\n", "  \n", "\t  mynodo* aux=front;\n", "\t  front = front->get_next();\n", "\t  tam--;\n", "\t  return (aux->get_dato());\n", "    \n", "\t}\n", "  }\n", "  \n", "}\n", "\n", "void cola::push(TDATO elemento) {\n", "  \n", "   mynodo* dummy = new mynodo(elemento);\n", "\n", "  if(empty()) {\n", "    \n", "    insertar_vacio(elemento);\n", "  \n", "  } else {\n", "    \n", "    rear->set_next(dummy);\n", "    rear=dummy;\n", "    \n", "  }\n", "  tam++;\n", "}\n", "\n", "\n", "\n", "void cola::escri<PERSON>(void) {\n", "  \n", "  \n", "  mynodo* aux = front;\n", "  \n", "  while(aux != NULL) {\n", "  \n", "    std::cout << aux->get_dato() << \" \";\n", "    aux= aux->get_next();\n", "  }\n", "  \n", "  std::cout << std::endl;\n", "  \n", "  //std::cout << tam << std::endl;\n", "}\n", "\n", "\n", "/COLA/main.cpp\n", "#include \"cola.hpp\"\n", "\n", "int main(void) {\n", " \n", "  cola A;\n", "  \n", "  \n", "    \n", "  \n", "  \n", "  <PERSON><PERSON>push(6);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>push(7);\n", "  <PERSON><PERSON>();\n", "  <PERSON><PERSON>push(8);\n", "  <PERSON><PERSON>();\n", "  \n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  A.pop();\n", "  <PERSON><PERSON>();\n", "  \n", "  return 0;\n", " \n", "}\n"]}], "source": ["for idx, row in enumerate(ds):\n", "    if idx == 5:\n", "        for file in row[\"files\"]:\n", "            print(file[\"path\"])\n", "            print(file[\"content\"])\n", "    if idx > 10:\n", "        break"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}